# Lesson Plan Content Generation Service

## Mô tả

Service xử lý sinh nội dung giáo án chi tiết bằng LLM theo cấu trúc JSON đầu vào. Service này được thiết kế để:

- Nhận đầu vào là cây JSON đại diện cho giáo án
- Sử dụng LLM để sinh nội dung chi tiết cho các node
- **Làm sạch nội dung** từ LLM bằng `clean_text_content` (loại bỏ markdown, ký tự đặc biệt)
- Đ<PERSON>m bảo cấu trúc JSON không thay đổi, chỉ bổ sung nội dung vào field `content`
- Xử lý đệ quy với kiểm soát vòng lặp và độ sâu

## Cấu trúc đầu vào

### JSON Structure

```json
{
  "id": 29,
  "lessonPlanId": 16,
  "parentId": null,
  "title": "<PERSON><PERSON> <PERSON><PERSON> tiê<PERSON>",
  "content": "",
  "type": "SECTION",
  "orderIndex": 1,
  "metadata": null,
  "status": "ACTIVE",
  "children": [...]
}
```

### Các trường bắt buộc

- `id`: ID duy nhất của node (integer)
- `type`: Loại node ("SECTION", "PARAGRAPH", "LIST_ITEM")
- `status`: Trạng thái ("ACTIVE", "INACTIVE", "DELETED")
- `title`: Tiêu đề của node
- `content`: Nội dung (sẽ được sinh bởi LLM)
- `children`: Mảng các node con (có thể rỗng)

### Các loại node (type)

- **SECTION**: Mục lớn, có thể có các node con
- **PARAGRAPH**: Đoạn văn mô tả, cần LLM phát triển chi tiết
- **LIST_ITEM**: Mục liệt kê, cần LLM sinh chi tiết

## Quy tắc xử lý

### 1. Chỉ xử lý node có status = "ACTIVE"

### 2. Quy tắc sinh nội dung:

- **PARAGRAPH/LIST_ITEM**: Luôn sinh nội dung chi tiết
- **SECTION có children PARAGRAPH/LIST_ITEM**: Không sinh content, để rỗng
- **SECTION không có children content**: Sinh content cho section này

### 3. Làm sạch nội dung:

- **Loại bỏ markdown formatting**: `*`, `#`, `_`, `` ` ``
- **Loại bỏ links**: `[text](url)` → `text`
- **Chuẩn hóa khoảng trắng**: Loại bỏ khoảng trắng thừa
- **Chuẩn hóa xuống dòng**: Loại bỏ nhiều dòng trống liên tiếp
- **Loại bỏ ký tự đặc biệt**: `\b`, `\r`, etc.

### 4. Validation:

- Kiểm tra vòng lặp (circular reference)
- Giới hạn độ sâu tối đa (MAX_DEPTH = 10)
- Đảm bảo cấu trúc JSON không thay đổi
- Tất cả node phải có field "content"

## Sử dụng

### 1. Import service

```python
from app.services.lesson_plan_content_service import lesson_plan_content_service
```

### 2. Gọi service

```python
result = await lesson_plan_content_service.generate_lesson_plan_content(
    lesson_plan_json=your_json_data,
    lesson_id="optional_lesson_id"  # Để lấy nội dung tham khảo
)
```

### 3. Xử lý kết quả

```python
if result["success"]:
    processed_json = result["lesson_plan"]
    statistics = result["statistics"]
    print(f"Processed {statistics['content_nodes_processed']} nodes")
else:
    print(f"Error: {result['error']}")
```

## API Endpoint

### POST `/api/lesson-plan/generate-lesson-plan-content`

#### Request Body:

```json
{
  "lesson_plan_json": {
    "id": 29,
    "lessonPlanId": 16,
    "title": "I. Mục tiêu",
    "content": "",
    "type": "SECTION",
    "status": "ACTIVE",
    "children": [...]
  },
  "lesson_id": "optional_lesson_id"
}
```

#### Response:

```json
{
  "success": true,
  "lesson_plan": {
    // JSON đã được sinh nội dung
  },
  "statistics": {
    "total_nodes": 10,
    "content_nodes_processed": 7,
    "lesson_content_used": true
  }
}
```

## Testing

Chạy test script:

```bash
python test_lesson_plan_content.py
```

## Lưu ý quan trọng

### 1. Performance

- Service sử dụng LLM nên có thể mất thời gian
- Số lượng node nhiều sẽ tăng thời gian xử lý
- Có thể cần timeout cho các request lớn

### 2. Error Handling

- Service có exception handling đầy đủ
- Validate đầu vào và đầu ra nghiêm ngặt
- Log chi tiết cho debugging

### 3. Extensibility

- Dễ dàng thêm type node mới
- Có thể tùy chỉnh prompt cho từng type
- Có thể mở rộng validation rules

### 4. Dependencies

- Cần LLM service (Gemini hoặc OpenRouter)
- Cần textbook retrieval service (optional)
- Cần các API keys được cấu hình

## Cấu trúc file

```
app/services/lesson_plan_content_service.py  # Main service
app/api/endpoints/lesson_plan.py            # API endpoint
test_lesson_plan_content.py                 # Test script
LESSON_PLAN_CONTENT_SERVICE.md             # Documentation
```

## Troubleshooting

### 1. LLM không available

- Kiểm tra API keys (GEMINI_API_KEY, OPENROUTER_API_KEY)
- Kiểm tra network connection

### 2. Validation errors

- Đảm bảo JSON có đúng structure
- Kiểm tra các trường bắt buộc
- Kiểm tra không có vòng lặp trong cấu trúc

### 3. Performance issues

- Giảm số lượng node cần xử lý
- Tăng timeout cho API calls
- Xem xét batch processing cho JSON lớn
