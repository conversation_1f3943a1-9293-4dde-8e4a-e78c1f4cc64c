"""
Test script cho Lesson Plan Content Service
"""

import asyncio
import json
from app.services.lesson_plan_content_service import lesson_plan_content_service


# Sample JSON data theo đặc tả
sample_lesson_plan = {
    "id": 29,
    "lessonPlanId": 16,
    "parentId": None,
    "title": "<PERSON><PERSON> tiêu",
    "content": "",
    "type": "SECTION",
    "orderIndex": 1,
    "metadata": None,
    "status": "ACTIVE",
    "children": [
        {
            "id": 30,
            "lessonPlanId": 16,
            "parentId": 29,
            "title": "1. <PERSON><PERSON><PERSON> thức",
            "content": "",
            "type": "SECTION",
            "orderIndex": 0,
            "metadata": None,
            "status": "ACTIVE",
            "children": [
                {
                    "id": 31,
                    "lessonPlanId": 16,
                    "parentId": 30,
                    "title": "Nhận biết và trình bày được kh<PERSON>i niệm…",
                    "content": "",
                    "type": "PARAGRAPH",
                    "orderIndex": 0,
                    "metadata": None,
                    "status": "ACTIVE",
                    "children": []
                },
                {
                    "id": 32,
                    "lessonPlanId": 16,
                    "parentId": 30,
                    "title": "<PERSON><PERSON><PERSON> đ<PERSON>c bản chất của hiện tượng/sự vật…",
                    "content": "",
                    "type": "PARAGRAPH",
                    "orderIndex": 1,
                    "metadata": None,
                    "status": "ACTIVE",
                    "children": []
                },
                {
                    "id": 33,
                    "lessonPlanId": 16,
                    "parentId": 30,
                    "title": "Phân biệt được các loại…",
                    "content": "",
                    "type": "LIST_ITEM",
                    "orderIndex": 2,
                    "metadata": None,
                    "status": "ACTIVE",
                    "children": []
                },
                {
                    "id": 34,
                    "lessonPlanId": 16,
                    "parentId": 30,
                    "title": "Vận dụng kiến thức để giải thích…",
                    "content": "",
                    "type": "PARAGRAPH",
                    "orderIndex": 3,
                    "metadata": None,
                    "status": "ACTIVE",
                    "children": []
                },
                {
                    "id": 35,
                    "lessonPlanId": 16,
                    "parentId": 30,
                    "title": "Phân tích được ưu điểm/nhược điểm của…",
                    "content": "",
                    "type": "LIST_ITEM",
                    "orderIndex": 4,
                    "metadata": None,
                    "status": "ACTIVE",
                    "children": []
                },
                {
                    "id": 36,
                    "lessonPlanId": 16,
                    "parentId": 30,
                    "title": "So sánh được đặc điểm của…",
                    "content": "",
                    "type": "LIST_ITEM",
                    "orderIndex": 5,
                    "metadata": None,
                    "status": "ACTIVE",
                    "children": []
                },
                {
                    "id": 37,
                    "lessonPlanId": 16,
                    "parentId": 30,
                    "title": "Ứng dụng kiến thức đã học vào thực tiễn…",
                    "content": "",
                    "type": "PARAGRAPH",
                    "orderIndex": 6,
                    "metadata": None,
                    "status": "ACTIVE",
                    "children": []
                }
            ]
        },
        {
            "id": 38,
            "lessonPlanId": 16,
            "parentId": 29,
            "title": "2. Kỹ năng",
            "content": "",
            "type": "SECTION",
            "orderIndex": 1,
            "metadata": None,
            "status": "ACTIVE",
            "children": [
                {
                    "id": 39,
                    "lessonPlanId": 16,
                    "parentId": 38,
                    "title": "Rèn luyện kỹ năng quan sát và phân tích",
                    "content": "",
                    "type": "PARAGRAPH",
                    "orderIndex": 0,
                    "metadata": None,
                    "status": "ACTIVE",
                    "children": []
                },
                {
                    "id": 40,
                    "lessonPlanId": 16,
                    "parentId": 38,
                    "title": "Phát triển kỹ năng làm việc nhóm",
                    "content": "",
                    "type": "LIST_ITEM",
                    "orderIndex": 1,
                    "metadata": None,
                    "status": "ACTIVE",
                    "children": []
                }
            ]
        }
    ]
}


async def test_lesson_plan_content_generation():
    """Test function để kiểm tra service"""
    print("=== TESTING LESSON PLAN CONTENT GENERATION ===")
    
    try:
        # Test với sample data
        print("\n1. Testing with sample lesson plan data...")
        result = await lesson_plan_content_service.generate_lesson_plan_content(
            lesson_plan_json=sample_lesson_plan,
            lesson_id=None  # Không có lesson_id để test
        )
        
        print(f"Success: {result['success']}")
        
        if result['success']:
            print(f"Statistics: {result.get('statistics', {})}")
            
            # In ra một vài node đã được sinh nội dung
            processed_plan = result['lesson_plan']
            print("\n2. Sample generated content:")
            
            def print_node_content(node, level=0):
                indent = "  " * level
                node_type = node.get('type', '')
                node_title = node.get('title', '')
                node_content = node.get('content', '')
                
                print(f"{indent}[{node_type}] {node_title}")
                if node_content.strip():
                    print(f"{indent}  Content: {node_content[:100]}...")
                
                for child in node.get('children', []):
                    print_node_content(child, level + 1)
            
            print_node_content(processed_plan)
            
        else:
            print(f"Error: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"Test failed with exception: {e}")
        import traceback
        traceback.print_exc()


async def test_validation():
    """Test validation functions"""
    print("\n=== TESTING VALIDATION ===")
    
    # Test invalid JSON
    invalid_json = {"invalid": "structure"}
    
    result = await lesson_plan_content_service.generate_lesson_plan_content(
        lesson_plan_json=invalid_json,
        lesson_id=None
    )
    
    print(f"Invalid JSON test - Success: {result['success']}")
    if not result['success']:
        print(f"Expected error: {result.get('error', 'No error message')}")


if __name__ == "__main__":
    # Chạy test
    asyncio.run(test_lesson_plan_content_generation())
    asyncio.run(test_validation())
