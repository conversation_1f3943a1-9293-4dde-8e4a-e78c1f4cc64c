"""
Ví dụ sử dụng API endpoint cho Lesson Plan Content Generation
"""

import requests
import json


# Sample data
sample_request = {
    "lesson_plan_json": {
        "id": 29,
        "lessonPlanId": 16,
        "parentId": None,
        "title": "<PERSON><PERSON> <PERSON>ục tiêu",
        "content": "",
        "type": "SECTION",
        "orderIndex": 1,
        "metadata": None,
        "status": "ACTIVE",
        "children": [
            {
                "id": 30,
                "lessonPlanId": 16,
                "parentId": 29,
                "title": "1. Kiến thức",
                "content": "",
                "type": "SECTION",
                "orderIndex": 0,
                "metadata": None,
                "status": "ACTIVE",
                "children": [
                    {
                        "id": 31,
                        "lessonPlanId": 16,
                        "parentId": 30,
                        "title": "Nhận biết và trình bày được khái niệm về động vật có xương sống",
                        "content": "",
                        "type": "PARAGRAPH",
                        "orderIndex": 0,
                        "metadata": None,
                        "status": "ACTIVE",
                        "children": []
                    },
                    {
                        "id": 32,
                        "lessonPlanId": 16,
                        "parentId": 30,
                        "title": "Hiểu được đặc điểm cấu tạo cơ thể của động vật có xương sống",
                        "content": "",
                        "type": "PARAGRAPH",
                        "orderIndex": 1,
                        "metadata": None,
                        "status": "ACTIVE",
                        "children": []
                    },
                    {
                        "id": 33,
                        "lessonPlanId": 16,
                        "parentId": 30,
                        "title": "Phân biệt được các nhóm động vật có xương sống",
                        "content": "",
                        "type": "LIST_ITEM",
                        "orderIndex": 2,
                        "metadata": None,
                        "status": "ACTIVE",
                        "children": []
                    }
                ]
            },
            {
                "id": 38,
                "lessonPlanId": 16,
                "parentId": 29,
                "title": "2. Kỹ năng",
                "content": "",
                "type": "SECTION",
                "orderIndex": 1,
                "metadata": None,
                "status": "ACTIVE",
                "children": [
                    {
                        "id": 39,
                        "lessonPlanId": 16,
                        "parentId": 38,
                        "title": "Rèn luyện kỹ năng quan sát và mô tả đặc điểm động vật",
                        "content": "",
                        "type": "PARAGRAPH",
                        "orderIndex": 0,
                        "metadata": None,
                        "status": "ACTIVE",
                        "children": []
                    }
                ]
            }
        ]
    },
    "lesson_id": None  # Optional
}


def test_api_endpoint():
    """Test API endpoint"""
    
    # URL của API (thay đổi theo cấu hình thực tế)
    api_url = "http://localhost:8000/api/lesson-plan/generate-lesson-plan-content"
    
    try:
        print("=== TESTING API ENDPOINT ===")
        print(f"Sending request to: {api_url}")
        
        # Gửi request
        response = requests.post(
            api_url,
            json=sample_request,
            headers={"Content-Type": "application/json"},
            timeout=120  # 2 minutes timeout
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"Success: {result.get('success', False)}")
            
            if result.get('success'):
                print(f"Statistics: {result.get('statistics', {})}")
                
                # In ra một vài node đã được sinh nội dung
                lesson_plan = result.get('lesson_plan', {})
                print("\nSample generated content:")
                
                def print_node_content(node, level=0):
                    indent = "  " * level
                    node_type = node.get('type', '')
                    node_title = node.get('title', '')
                    node_content = node.get('content', '')
                    
                    print(f"{indent}[{node_type}] {node_title}")
                    if node_content.strip():
                        # Chỉ in 100 ký tự đầu
                        content_preview = node_content[:100] + "..." if len(node_content) > 100 else node_content
                        print(f"{indent}  Content: {content_preview}")
                    
                    for child in node.get('children', []):
                        print_node_content(child, level + 1)
                
                print_node_content(lesson_plan)
                
            else:
                print(f"Error: {result.get('error', 'Unknown error')}")
        else:
            print(f"HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.Timeout:
        print("Request timeout - API took too long to respond")
    except requests.exceptions.ConnectionError:
        print("Connection error - Make sure the API server is running")
    except Exception as e:
        print(f"Error: {e}")


def test_with_curl():
    """Tạo curl command để test"""
    
    curl_command = f"""
curl -X POST "http://localhost:8000/api/lesson-plan/generate-lesson-plan-content" \\
     -H "Content-Type: application/json" \\
     -d '{json.dumps(sample_request, ensure_ascii=False, indent=2)}'
"""
    
    print("=== CURL COMMAND FOR TESTING ===")
    print(curl_command)


if __name__ == "__main__":
    # Test API endpoint
    test_api_endpoint()
    
    print("\n" + "="*50)
    
    # In curl command
    test_with_curl()
