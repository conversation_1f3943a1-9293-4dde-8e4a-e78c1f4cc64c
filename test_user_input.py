#!/usr/bin/env python3
"""
Test với dữ liệu chính xác từ user để kiểm tra logic SECTION/SUBSECTION
"""

import asyncio
import json
from app.services.lesson_plan_content_service import LessonPlanContentService

# Khởi tạo service
lesson_plan_content_service = LessonPlanContentService()

async def test_user_exact_input():
    """Test với dữ liệu chính xác từ user"""
    print("=== TESTING USER EXACT INPUT ===")
    
    # Dữ liệu chính xác từ user
    user_input = {
        "lesson_plan_json": {
            "id": 64,
            "lessonPlanId": 16,
            "parentId": None,
            "title": "III. TIẾN TRÌNH DẠY HỌC",
            "content": "",
            "type": "SECTION",
            "orderIndex": 3,
            "metadata": None,
            "status": "ACTIVE",
            "children": [
                {
                    "id": 65,
                    "lessonPlanId": 16,
                    "parentId": 64,
                    "title": "1. <PERSON><PERSON><PERSON> động 1: Khởi động",
                    "content": "",
                    "type": "SUBSECTION",
                    "orderIndex": 0,
                    "metadata": None,
                    "status": "ACTIVE",
                    "children": [
                        {
                            "id": 66,
                            "lessonPlanId": 16,
                            "parentId": 65,
                            "title": "a. Mục tiêu",
                            "content": "",
                            "type": "LIST_ITEM",
                            "orderIndex": 0,
                            "metadata": None,
                            "status": "ACTIVE",
                            "children": []
                        },
                        {
                            "id": 67,
                            "lessonPlanId": 16,
                            "parentId": 65,
                            "title": "b. Nội dung",
                            "content": "",
                            "type": "LIST_ITEM",
                            "orderIndex": 1,
                            "metadata": None,
                            "status": "ACTIVE",
                            "children": [
                                {
                                    "id": 68,
                                    "lessonPlanId": 16,
                                    "parentId": 67,
                                    "title": "CÂU HỎI KHỞI ĐỘNG",
                                    "content": "",
                                    "type": "PARAGRAPH",
                                    "orderIndex": 0,
                                    "metadata": None,
                                    "status": "ACTIVE",
                                    "children": []
                                },
                                {
                                    "id": 69,
                                    "lessonPlanId": 16,
                                    "parentId": 67,
                                    "title": "Hình ảnh, video mô tả cho câu hỏi khởi động",
                                    "content": "",
                                    "type": "PARAGRAPH",
                                    "orderIndex": 1,
                                    "metadata": None,
                                    "status": "ACTIVE",
                                    "children": []
                                }
                            ]
                        }
                    ]
                }
            ]
        },
        "lesson_id": "1"
    }
    
    # Gọi service
    result = await lesson_plan_content_service.generate_lesson_plan_content(
        lesson_plan_json=user_input["lesson_plan_json"],
        lesson_id=user_input["lesson_id"]
    )
    
    print(f"Success: {result['success']}")
    
    if result['success']:
        lesson_plan = result['lesson_plan']
        
        # Kiểm tra từng node
        print("\n=== KIỂM TRA KẾT QUẢ ===")
        
        # Node 64 (SECTION) - có children nên content = ""
        node_64_content = lesson_plan.get('content', '')
        print(f"Node 64 (SECTION) content: '{node_64_content}' - Expected: '' ✅" if node_64_content == "" else f"Node 64 (SECTION) content: '{node_64_content}' - Expected: '' ❌")
        
        # Node 65 (SUBSECTION) - có children nên content = ""
        node_65 = lesson_plan['children'][0]
        node_65_content = node_65.get('content', '')
        print(f"Node 65 (SUBSECTION) content: '{node_65_content}' - Expected: '' ✅" if node_65_content == "" else f"Node 65 (SUBSECTION) content: '{node_65_content}' - Expected: '' ❌")
        
        # Node 66 (LIST_ITEM) - không có children nên có content
        node_66 = node_65['children'][0]
        node_66_content = node_66.get('content', '')
        print(f"Node 66 (LIST_ITEM) content length: {len(node_66_content)} chars - Expected: > 0 ✅" if len(node_66_content) > 0 else f"Node 66 (LIST_ITEM) content length: {len(node_66_content)} chars - Expected: > 0 ❌")
        
        # Node 67 (LIST_ITEM) - có children nên có content (vì LIST_ITEM luôn sinh content)
        node_67 = node_65['children'][1]
        node_67_content = node_67.get('content', '')
        print(f"Node 67 (LIST_ITEM) content length: {len(node_67_content)} chars - Expected: > 0 ✅" if len(node_67_content) > 0 else f"Node 67 (LIST_ITEM) content length: {len(node_67_content)} chars - Expected: > 0 ❌")
        
        # Node 68, 69 (PARAGRAPH) - không có children nên có content
        node_68 = node_67['children'][0]
        node_68_content = node_68.get('content', '')
        print(f"Node 68 (PARAGRAPH) content length: {len(node_68_content)} chars - Expected: > 0 ✅" if len(node_68_content) > 0 else f"Node 68 (PARAGRAPH) content length: {len(node_68_content)} chars - Expected: > 0 ❌")
        
        node_69 = node_67['children'][1]
        node_69_content = node_69.get('content', '')
        print(f"Node 69 (PARAGRAPH) content length: {len(node_69_content)} chars - Expected: > 0 ✅" if len(node_69_content) > 0 else f"Node 69 (PARAGRAPH) content length: {len(node_69_content)} chars - Expected: > 0 ❌")
        
        # Tổng kết
        print(f"\nStatistics: {result.get('statistics', {})}")
        
        # Validation tổng thể
        validation_passed = (
            node_64_content == "" and  # SECTION có children
            node_65_content == "" and  # SUBSECTION có children  
            len(node_66_content) > 0 and  # LIST_ITEM không có children
            len(node_67_content) > 0 and  # LIST_ITEM có children (nhưng vẫn sinh content)
            len(node_68_content) > 0 and  # PARAGRAPH không có children
            len(node_69_content) > 0      # PARAGRAPH không có children
        )
        
        if validation_passed:
            print("\n🎉 PASSED: Logic xử lý hoàn toàn đúng!")
        else:
            print("\n❌ FAILED: Logic xử lý có vấn đề!")
            
    else:
        print(f"Error: {result.get('error', 'Unknown error')}")

if __name__ == "__main__":
    asyncio.run(test_user_exact_input())
